#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线资源下载工具

该工具用于下载图表可视化所需的JavaScript资源，
以支持完全离线运行。

使用方法:
    python tools/download_offline_resources.py

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import requests
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineResourceDownloader:
    """离线资源下载器"""
    
    def __init__(self):
        self.project_root = project_root
        self.js_dir = self.project_root / "src" / "resources" / "js"
        
        # 需要下载的资源列表
        self.resources = {
            "echarts.min.js": "https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js",
            "echarts-wordcloud.min.js": "https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js"
        }
    
    def create_directories(self):
        """创建必要的目录"""
        try:
            self.js_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {self.js_dir}")
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            return False
    
    def download_file(self, filename: str, url: str) -> bool:
        """
        下载单个文件
        
        参数:
            filename: 文件名
            url: 下载URL
            
        返回:
            bool: 下载是否成功
        """
        file_path = self.js_dir / filename
        
        try:
            logger.info(f"开始下载: {filename}")
            logger.info(f"URL: {url}")
            
            # 发送HTTP请求
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            # 验证文件大小
            file_size = file_path.stat().st_size
            logger.info(f"下载完成: {filename} ({file_size} bytes)")
            
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"下载失败 {filename}: 网络错误 - {e}")
            return False
        except Exception as e:
            logger.error(f"下载失败 {filename}: {e}")
            return False
    
    def download_all_resources(self) -> bool:
        """
        下载所有资源
        
        返回:
            bool: 是否全部下载成功
        """
        logger.info("开始下载离线资源...")
        
        # 创建目录
        if not self.create_directories():
            return False
        
        success_count = 0
        total_count = len(self.resources)
        
        # 下载每个资源
        for filename, url in self.resources.items():
            if self.download_file(filename, url):
                success_count += 1
            else:
                logger.warning(f"跳过失败的文件: {filename}")
        
        # 报告结果
        logger.info(f"下载完成: {success_count}/{total_count} 个文件成功")
        
        if success_count == total_count:
            logger.info("✅ 所有资源下载成功！图表功能现在可以完全离线运行。")
            return True
        elif success_count > 0:
            logger.warning("⚠️ 部分资源下载成功，图表功能可能受限。")
            return False
        else:
            logger.error("❌ 所有资源下载失败，图表功能需要网络连接。")
            return False
    
    def check_existing_resources(self) -> dict:
        """
        检查已存在的资源
        
        返回:
            dict: 资源状态
        """
        status = {
            "total": len(self.resources),
            "existing": 0,
            "missing": [],
            "files": {}
        }
        
        for filename in self.resources.keys():
            file_path = self.js_dir / filename
            if file_path.exists():
                status["existing"] += 1
                status["files"][filename] = {
                    "exists": True,
                    "size": file_path.stat().st_size
                }
            else:
                status["missing"].append(filename)
                status["files"][filename] = {
                    "exists": False,
                    "size": 0
                }
        
        return status
    
    def print_status_report(self):
        """打印状态报告"""
        print("\n" + "="*60)
        print("离线资源状态报告")
        print("="*60)
        
        status = self.check_existing_resources()
        
        print(f"资源目录: {self.js_dir}")
        print(f"总计文件: {status['total']}")
        print(f"已存在: {status['existing']}")
        print(f"缺失: {len(status['missing'])}")
        
        print("\n文件详情:")
        for filename, info in status["files"].items():
            if info["exists"]:
                print(f"  ✅ {filename} ({info['size']} bytes)")
            else:
                print(f"  ❌ {filename} (缺失)")
        
        if status["missing"]:
            print(f"\n缺失文件: {', '.join(status['missing'])}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    print("智能驾驶试验管控工具 - 离线资源下载器")
    print("="*60)
    
    downloader = OfflineResourceDownloader()
    
    # 显示当前状态
    downloader.print_status_report()
    
    # 检查是否需要下载
    status = downloader.check_existing_resources()
    if not status["missing"]:
        print("\n✅ 所有资源已存在，无需下载。")
        return
    
    # 询问用户是否继续
    print(f"\n需要下载 {len(status['missing'])} 个文件。")
    response = input("是否继续下载？(y/N): ").strip().lower()
    
    if response not in ['y', 'yes', '是']:
        print("下载已取消。")
        return
    
    # 开始下载
    try:
        success = downloader.download_all_resources()
        
        # 显示最终状态
        print("\n" + "="*60)
        downloader.print_status_report()
        
        if success:
            print("\n🎉 离线资源配置完成！")
            print("现在可以在没有网络连接的环境下使用图表功能。")
        else:
            print("\n⚠️ 部分资源下载失败。")
            print("建议检查网络连接后重试。")
            
    except KeyboardInterrupt:
        print("\n\n下载被用户中断。")
    except Exception as e:
        logger.error(f"下载过程中发生错误: {e}")


if __name__ == "__main__":
    main()
