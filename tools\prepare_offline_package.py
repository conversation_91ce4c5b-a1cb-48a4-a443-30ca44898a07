#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线部署包准备工具

该工具用于准备完整的离线部署包，
包括所有必需的文件和资源。

使用方法:
    python tools/prepare_offline_package.py

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflinePackagePreparer:
    """离线部署包准备器"""
    
    def __init__(self):
        self.project_root = project_root
        self.package_name = f"智能驾驶试验管控工具_离线版_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.package_dir = self.project_root.parent / self.package_name
        
        # 需要包含的文件和目录
        self.include_patterns = [
            "src/",
            "config/",
            "app/",
            "tools/",
            "*.py",
            "*.bat",
            "*.md",
            "*.txt",
            "*.db",
            "*.json",
            ".venv/",
            "python/"  # 如果存在嵌入式Python
        ]
        
        # 需要排除的文件和目录
        self.exclude_patterns = [
            "__pycache__/",
            "*.pyc",
            "*.pyo",
            ".git/",
            ".gitignore",
            "temp/",
            "*.log",
            "*.tmp"
        ]
    
    def should_include_file(self, file_path: Path) -> bool:
        """判断是否应该包含文件"""
        relative_path = file_path.relative_to(self.project_root)
        path_str = str(relative_path).replace('\\', '/')
        
        # 检查排除模式
        for pattern in self.exclude_patterns:
            if pattern.endswith('/'):
                if pattern[:-1] in path_str.split('/'):
                    return False
            elif pattern.startswith('*.'):
                if path_str.endswith(pattern[1:]):
                    return False
            elif pattern in path_str:
                return False
        
        # 检查包含模式
        for pattern in self.include_patterns:
            if pattern.endswith('/'):
                if path_str.startswith(pattern):
                    return True
            elif pattern.startswith('*.'):
                if path_str.endswith(pattern[1:]):
                    return True
            elif pattern == path_str or path_str.startswith(pattern + '/'):
                return True
        
        return False
    
    def copy_project_files(self):
        """复制项目文件"""
        logger.info("复制项目文件...")
        
        # 创建包目录
        self.package_dir.mkdir(parents=True, exist_ok=True)
        
        copied_files = 0
        skipped_files = 0
        
        for root, dirs, files in os.walk(self.project_root):
            root_path = Path(root)
            
            # 跳过不需要的目录
            dirs[:] = [d for d in dirs if not any(
                pattern.rstrip('/') == d for pattern in self.exclude_patterns if pattern.endswith('/')
            )]
            
            for file in files:
                file_path = root_path / file
                
                if self.should_include_file(file_path):
                    # 计算目标路径
                    relative_path = file_path.relative_to(self.project_root)
                    target_path = self.package_dir / relative_path
                    
                    # 创建目标目录
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    try:
                        shutil.copy2(file_path, target_path)
                        copied_files += 1
                        
                        if copied_files % 100 == 0:
                            logger.info(f"已复制 {copied_files} 个文件...")
                            
                    except Exception as e:
                        logger.warning(f"复制文件失败 {file_path}: {e}")
                        skipped_files += 1
                else:
                    skipped_files += 1
        
        logger.info(f"文件复制完成: {copied_files} 个文件已复制, {skipped_files} 个文件已跳过")
    
    def verify_offline_resources(self) -> bool:
        """验证离线资源"""
        logger.info("验证离线资源...")
        
        js_dir = self.package_dir / "src" / "resources" / "js"
        required_files = ["echarts.min.js", "echarts-wordcloud.min.js"]
        
        missing_files = []
        for filename in required_files:
            file_path = js_dir / filename
            if not file_path.exists():
                missing_files.append(filename)
        
        if missing_files:
            logger.error(f"缺失离线资源: {missing_files}")
            logger.info("请先运行: python tools/download_offline_resources.py")
            return False
        else:
            logger.info("✅ 离线资源验证通过")
            return True
    
    def create_deployment_guide(self):
        """创建部署指南"""
        logger.info("创建部署指南...")
        
        guide_content = f"""# {self.package_name} - 部署指南

## 📋 部署说明

这是智能驾驶试验管控工具的完整离线部署包，包含所有必需的文件和资源。

### 🎯 特性
- ✅ 完全离线运行
- ✅ 包含所有依赖
- ✅ 图表功能离线就绪
- ✅ 一键启动

### 🚀 部署步骤

1. **复制文件**
   - 将整个文件夹复制到目标计算机
   - 建议放置在简单路径下（如 C:\\TMS\\ 或 D:\\工具\\）

2. **启动应用**
   - 双击 `offline_launcher.bat` 启动应用
   - 或者双击 `启动应用程序.bat`（中文版）

3. **验证功能**
   - 检查主界面是否正常显示
   - 测试数据表格功能
   - 验证图表创建功能
   - 确认所有菜单正常工作

### 🔧 故障排除

如果遇到问题，请按以下顺序尝试：

1. **运行验证工具**
   ```
   python tools/validate_offline_deployment.py
   ```

2. **检查离线就绪状态**
   ```
   python tools/check_offline_readiness.py
   ```

3. **修复虚拟环境**（如果需要）
   ```
   python fix_venv_complete.py
   ```

### 📊 功能清单

#### 完全离线功能
- ✅ 数据管理和表格操作
- ✅ 项目创建和管理
- ✅ Excel文件导入导出
- ✅ 数据筛选和排序
- ✅ 公式计算
- ✅ 图表创建（柱状图、折线图、饼图等）
- ✅ 数据可视化看板
- ✅ 词云图生成
- ✅ 图像查看和处理
- ✅ 用户设置管理

### 📞 技术支持

如果遇到技术问题，请提供以下信息：
- 操作系统版本
- 错误信息截图
- 验证工具的输出结果

---
部署包创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
版本: 离线完整版
"""
        
        guide_path = self.package_dir / "部署指南.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        logger.info(f"部署指南已创建: {guide_path}")
    
    def create_verification_script(self):
        """创建验证脚本"""
        logger.info("创建验证脚本...")
        
        script_content = """@echo off
chcp 65001 >nul 2>&1
echo 智能驾驶试验管控工具 - 离线部署验证
echo ========================================
echo.

echo 正在验证离线部署...
python tools/validate_offline_deployment.py

echo.
echo 验证完成！
pause
"""
        
        script_path = self.package_dir / "验证部署.bat"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        logger.info(f"验证脚本已创建: {script_path}")
    
    def prepare_package(self) -> bool:
        """准备完整的部署包"""
        logger.info(f"开始准备离线部署包: {self.package_name}")
        
        try:
            # 1. 复制项目文件
            self.copy_project_files()
            
            # 2. 验证离线资源
            if not self.verify_offline_resources():
                return False
            
            # 3. 创建部署指南
            self.create_deployment_guide()
            
            # 4. 创建验证脚本
            self.create_verification_script()
            
            # 5. 显示包信息
            package_size = sum(f.stat().st_size for f in self.package_dir.rglob('*') if f.is_file())
            package_size_mb = package_size / (1024 * 1024)
            
            logger.info(f"✅ 离线部署包准备完成!")
            logger.info(f"📦 包位置: {self.package_dir}")
            logger.info(f"📊 包大小: {package_size_mb:.1f} MB")
            
            return True
            
        except Exception as e:
            logger.error(f"准备部署包失败: {e}")
            return False
    
    def print_summary(self):
        """打印摘要信息"""
        print("\n" + "="*80)
        print("智能驾驶试验管控工具 - 离线部署包准备完成")
        print("="*80)
        print(f"📦 包名称: {self.package_name}")
        print(f"📁 包位置: {self.package_dir}")
        
        if self.package_dir.exists():
            package_size = sum(f.stat().st_size for f in self.package_dir.rglob('*') if f.is_file())
            package_size_mb = package_size / (1024 * 1024)
            file_count = len(list(self.package_dir.rglob('*')))
            
            print(f"📊 包大小: {package_size_mb:.1f} MB")
            print(f"📄 文件数量: {file_count}")
        
        print("\n🚀 部署说明:")
        print("1. 将整个文件夹复制到目标计算机")
        print("2. 双击 'offline_launcher.bat' 启动应用")
        print("3. 运行 '验证部署.bat' 验证功能")
        
        print("\n✅ 功能特性:")
        print("- 完全离线运行")
        print("- 包含所有依赖")
        print("- 图表功能离线就绪")
        print("- 一键启动和验证")
        
        print("="*80)


def main():
    """主函数"""
    preparer = OfflinePackagePreparer()
    
    if preparer.prepare_package():
        preparer.print_summary()
        print("\n🎉 离线部署包准备成功！")
    else:
        print("\n❌ 离线部署包准备失败，请检查错误信息。")


if __name__ == "__main__":
    main()
