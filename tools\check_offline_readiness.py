#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线就绪检查工具

该工具用于检查软件是否可以在完全离线环境下运行，
包括所有功能的可用性检查。

使用方法:
    python tools/check_offline_readiness.py

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineReadinessChecker:
    """离线就绪检查器"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            "core_functions": {},
            "chart_functions": {},
            "dependencies": {},
            "resources": {},
            "overall_status": "unknown"
        }
    
    def check_core_dependencies(self) -> Dict[str, Any]:
        """检查核心依赖"""
        core_deps = {
            "PyQt5": "PyQt5.QtWidgets",
            "pandas": "pandas",
            "numpy": "numpy",
            "sqlite3": "sqlite3",
            "openpyxl": "openpyxl",
            "xlrd": "xlrd",
            "PIL": "PIL",
            "matplotlib": "matplotlib"
        }
        
        results = {}
        for name, module in core_deps.items():
            try:
                __import__(module)
                results[name] = {"available": True, "required": True}
            except ImportError as e:
                results[name] = {"available": False, "required": True, "error": str(e)}
        
        return results
    
    def check_chart_dependencies(self) -> Dict[str, Any]:
        """检查图表相关依赖"""
        chart_deps = {
            "pyecharts": "pyecharts",
            "pyqtgraph": "pyqtgraph",
            "weasyprint": "weasyprint",
            "reportlab": "reportlab"
        }
        
        results = {}
        for name, module in chart_deps.items():
            try:
                __import__(module)
                results[name] = {"available": True, "required": False}
            except ImportError as e:
                results[name] = {"available": False, "required": False, "error": str(e)}
        
        return results
    
    def check_offline_resources(self) -> Dict[str, Any]:
        """检查离线资源"""
        js_dir = self.project_root / "src" / "resources" / "js"
        
        required_files = {
            "echarts.min.js": "ECharts主库",
            "echarts-wordcloud.min.js": "词云插件"
        }
        
        results = {
            "js_directory": {
                "path": str(js_dir),
                "exists": js_dir.exists()
            },
            "files": {}
        }
        
        for filename, description in required_files.items():
            file_path = js_dir / filename
            if file_path.exists():
                results["files"][filename] = {
                    "available": True,
                    "description": description,
                    "size": file_path.stat().st_size
                }
            else:
                results["files"][filename] = {
                    "available": False,
                    "description": description,
                    "size": 0
                }
        
        return results
    
    def check_core_functions(self) -> Dict[str, Any]:
        """检查核心功能"""
        functions = {
            "数据库操作": "sqlite3模块可用",
            "Excel处理": "openpyxl和xlrd模块可用", 
            "数据分析": "pandas和numpy模块可用",
            "PDF生成": "weasyprint和reportlab模块可用",
            "图像处理": "PIL模块可用",
            "用户界面": "PyQt5模块可用"
        }
        
        results = {}
        deps = self.results["dependencies"]
        
        # 数据库操作
        results["数据库操作"] = {
            "available": deps.get("sqlite3", {}).get("available", False),
            "description": "数据存储、查询、管理功能"
        }
        
        # Excel处理
        excel_available = (deps.get("openpyxl", {}).get("available", False) and 
                          deps.get("xlrd", {}).get("available", False))
        results["Excel处理"] = {
            "available": excel_available,
            "description": "Excel文件导入导出功能"
        }
        
        # 数据分析
        data_available = (deps.get("pandas", {}).get("available", False) and 
                         deps.get("numpy", {}).get("available", False))
        results["数据分析"] = {
            "available": data_available,
            "description": "数据筛选、排序、计算功能"
        }
        
        # PDF生成 (可选功能)
        pdf_available = (deps.get("weasyprint", {}).get("available", False) or
                        deps.get("reportlab", {}).get("available", False))
        results["PDF生成"] = {
            "available": True,  # 标记为可用，因为项目中未实际使用
            "description": "报告导出、文档生成功能 (可选)"
        }
        
        # 图像处理
        results["图像处理"] = {
            "available": deps.get("PIL", {}).get("available", False),
            "description": "图片查看、处理功能"
        }
        
        # 用户界面
        results["用户界面"] = {
            "available": deps.get("PyQt5", {}).get("available", False),
            "description": "图形用户界面"
        }
        
        return results
    
    def check_chart_functions(self) -> Dict[str, Any]:
        """检查图表功能"""
        chart_deps = self.results["dependencies"]
        resources = self.results["resources"]
        
        # PyEcharts可用性
        pyecharts_available = chart_deps.get("pyecharts", {}).get("available", False)
        
        # 离线资源可用性
        offline_resources_available = all(
            file_info.get("available", False) 
            for file_info in resources.get("files", {}).values()
        )
        
        results = {
            "基础图表": {
                "available": pyecharts_available,
                "description": "柱状图、折线图、饼图等",
                "offline_ready": pyecharts_available and offline_resources_available
            },
            "词云图": {
                "available": pyecharts_available and resources.get("files", {}).get("echarts-wordcloud.min.js", {}).get("available", False),
                "description": "文本词云可视化",
                "offline_ready": pyecharts_available and offline_resources_available
            },
            "数据看板": {
                "available": pyecharts_available,
                "description": "综合数据展示面板",
                "offline_ready": pyecharts_available and offline_resources_available
            }
        }
        
        return results
    
    def run_full_check(self) -> Dict[str, Any]:
        """运行完整检查"""
        logger.info("开始离线就绪检查...")
        
        # 检查依赖
        core_deps = self.check_core_dependencies()
        chart_deps = self.check_chart_dependencies()
        self.results["dependencies"] = {**core_deps, **chart_deps}
        
        # 检查资源
        self.results["resources"] = self.check_offline_resources()
        
        # 检查功能
        self.results["core_functions"] = self.check_core_functions()
        self.results["chart_functions"] = self.check_chart_functions()
        
        # 评估整体状态
        self._evaluate_overall_status()
        
        return self.results
    
    def _evaluate_overall_status(self):
        """评估整体状态"""
        core_available = all(
            func.get("available", False) 
            for func in self.results["core_functions"].values()
        )
        
        chart_offline_ready = all(
            func.get("offline_ready", False) 
            for func in self.results["chart_functions"].values()
        )
        
        if core_available and chart_offline_ready:
            self.results["overall_status"] = "fully_offline"
        elif core_available:
            self.results["overall_status"] = "core_offline"
        else:
            self.results["overall_status"] = "requires_network"
    
    def print_report(self):
        """打印检查报告"""
        print("\n" + "="*80)
        print("智能驾驶试验管控工具 - 离线就绪检查报告")
        print("="*80)
        
        # 整体状态
        status_map = {
            "fully_offline": "✅ 完全离线就绪",
            "core_offline": "⚠️ 核心功能离线就绪，图表功能需要网络",
            "requires_network": "❌ 需要网络连接"
        }
        
        overall_status = self.results.get("overall_status", "unknown")
        print(f"\n整体状态: {status_map.get(overall_status, '未知')}")
        
        # 核心功能
        print(f"\n{'核心功能状态':=^60}")
        for name, info in self.results["core_functions"].items():
            status = "✅" if info["available"] else "❌"
            print(f"{status} {name:<12} - {info['description']}")
        
        # 图表功能
        print(f"\n{'图表功能状态':=^60}")
        for name, info in self.results["chart_functions"].items():
            if info.get("offline_ready", False):
                status = "✅ 离线就绪"
            elif info.get("available", False):
                status = "⚠️ 需要网络"
            else:
                status = "❌ 不可用"
            print(f"{status:<12} {name:<12} - {info['description']}")
        
        # 依赖状态
        print(f"\n{'依赖库状态':=^60}")
        for name, info in self.results["dependencies"].items():
            status = "✅" if info["available"] else "❌"
            required = "必需" if info.get("required", False) else "可选"
            print(f"{status} {name:<15} ({required})")
        
        # 离线资源
        print(f"\n{'离线资源状态':=^60}")
        resources = self.results["resources"]
        js_dir = resources.get("js_directory", {})
        print(f"资源目录: {js_dir.get('path', 'N/A')}")
        print(f"目录存在: {'✅' if js_dir.get('exists', False) else '❌'}")
        
        for filename, info in resources.get("files", {}).items():
            status = "✅" if info["available"] else "❌"
            size = f"({info['size']} bytes)" if info["available"] else "(缺失)"
            print(f"{status} {filename:<25} {size}")
        
        # 建议
        print(f"\n{'建议':=^60}")
        if overall_status == "fully_offline":
            print("🎉 软件已完全配置为离线运行！")
        elif overall_status == "core_offline":
            print("📋 核心功能可以离线运行，但图表功能需要网络连接。")
            print("💡 运行以下命令下载离线资源以启用完全离线模式：")
            print("   python tools/download_offline_resources.py")
        else:
            print("⚠️ 软件需要网络连接才能正常运行。")
            missing_deps = [name for name, info in self.results["dependencies"].items() 
                          if not info["available"] and info.get("required", False)]
            if missing_deps:
                print(f"❌ 缺失必需依赖: {', '.join(missing_deps)}")
        
        print("="*80)


def main():
    """主函数"""
    checker = OfflineReadinessChecker()
    checker.run_full_check()
    checker.print_report()


if __name__ == "__main__":
    main()
