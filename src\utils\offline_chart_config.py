"""
离线图表配置模块

该模块用于配置PyEcharts在离线环境下的运行，
包括本地JavaScript资源的管理和配置。

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import logging
from typing import Optional
from pyecharts.globals import CurrentConfig

logger = logging.getLogger(__name__)


class OfflineChartConfig:
    """离线图表配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.local_js_path = self._get_local_js_path()
        
    def _get_local_js_path(self) -> str:
        """获取本地JavaScript资源路径"""
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 本地JavaScript资源路径
        js_path = os.path.join(project_root, "src", "resources", "js")
        return js_path
    
    def setup_offline_mode(self) -> bool:
        """
        设置PyEcharts离线模式
        
        返回:
            bool: 设置是否成功
        """
        try:
            # 检查本地JavaScript资源是否存在
            echarts_file = os.path.join(self.local_js_path, "echarts.min.js")
            wordcloud_file = os.path.join(self.local_js_path, "echarts-wordcloud.min.js")
            
            if os.path.exists(echarts_file) and os.path.exists(wordcloud_file):
                # 设置PyEcharts使用本地资源
                local_host = f"file:///{self.local_js_path.replace(os.sep, '/')}/"
                CurrentConfig.ONLINE_HOST = local_host
                
                self.logger.info(f"PyEcharts配置为离线模式，本地资源路径: {local_host}")
                return True
            else:
                self.logger.warning("本地JavaScript资源不存在，将使用在线模式")
                return False
                
        except Exception as e:
            self.logger.error(f"设置离线模式失败: {e}")
            return False
    
    def check_offline_resources(self) -> dict:
        """
        检查离线资源状态
        
        返回:
            dict: 资源状态信息
        """
        status = {
            "offline_ready": False,
            "echarts_available": False,
            "wordcloud_available": False,
            "local_path": self.local_js_path,
            "missing_files": []
        }
        
        try:
            # 检查目录是否存在
            if not os.path.exists(self.local_js_path):
                status["missing_files"].append("JavaScript资源目录")
                return status
            
            # 检查ECharts主文件
            echarts_file = os.path.join(self.local_js_path, "echarts.min.js")
            if os.path.exists(echarts_file):
                status["echarts_available"] = True
            else:
                status["missing_files"].append("echarts.min.js")
            
            # 检查词云插件文件
            wordcloud_file = os.path.join(self.local_js_path, "echarts-wordcloud.min.js")
            if os.path.exists(wordcloud_file):
                status["wordcloud_available"] = True
            else:
                status["missing_files"].append("echarts-wordcloud.min.js")
            
            # 判断是否完全离线就绪
            status["offline_ready"] = status["echarts_available"] and status["wordcloud_available"]
            
        except Exception as e:
            self.logger.error(f"检查离线资源失败: {e}")
            status["error"] = str(e)
        
        return status
    
    def create_offline_resources_directory(self) -> bool:
        """
        创建离线资源目录
        
        返回:
            bool: 创建是否成功
        """
        try:
            os.makedirs(self.local_js_path, exist_ok=True)
            self.logger.info(f"创建离线资源目录: {self.local_js_path}")
            return True
        except Exception as e:
            self.logger.error(f"创建离线资源目录失败: {e}")
            return False
    
    def get_offline_instructions(self) -> str:
        """
        获取离线部署说明
        
        返回:
            str: 离线部署说明文本
        """
        instructions = f"""
离线图表功能部署说明：

1. 创建JavaScript资源目录：
   {self.local_js_path}

2. 下载必需的JavaScript文件：
   - echarts.min.js (从 https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js)
   - echarts-wordcloud.min.js (从 https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min.js)

3. 将下载的文件放置到资源目录中

4. 重新启动应用程序，图表功能将自动使用本地资源

注意：如果本地资源不存在，系统将自动回退到在线模式（需要网络连接）。
"""
        return instructions


# 全局配置实例
offline_config = OfflineChartConfig()


def setup_offline_charts() -> bool:
    """
    设置离线图表模式的便捷函数
    
    返回:
        bool: 设置是否成功
    """
    return offline_config.setup_offline_mode()


def check_chart_offline_status() -> dict:
    """
    检查图表离线状态的便捷函数
    
    返回:
        dict: 离线状态信息
    """
    return offline_config.check_offline_resources()
