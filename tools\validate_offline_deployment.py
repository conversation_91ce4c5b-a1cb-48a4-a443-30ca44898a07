#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
离线部署验证工具

该工具用于验证软件在离线环境下的完整功能，
包括模拟离线环境和功能测试。

使用方法:
    python tools/validate_offline_deployment.py

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineDeploymentValidator:
    """离线部署验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {
            "core_imports": {},
            "chart_functionality": {},
            "offline_resources": {},
            "database_operations": {},
            "file_operations": {},
            "overall_score": 0
        }
    
    def test_core_imports(self) -> Dict[str, bool]:
        """测试核心模块导入"""
        logger.info("测试核心模块导入...")
        
        core_modules = [
            ("PyQt5", "PyQt5.QtWidgets"),
            ("pandas", "pandas"),
            ("numpy", "numpy"),
            ("sqlite3", "sqlite3"),
            ("openpyxl", "openpyxl"),
            ("PIL", "PIL"),
            ("matplotlib", "matplotlib"),
            ("pyecharts", "pyecharts")
        ]
        
        results = {}
        for name, module in core_modules:
            try:
                __import__(module)
                results[name] = True
                logger.info(f"✅ {name} 导入成功")
            except ImportError as e:
                results[name] = False
                logger.error(f"❌ {name} 导入失败: {e}")
        
        self.test_results["core_imports"] = results
        return results
    
    def test_chart_functionality(self) -> Dict[str, bool]:
        """测试图表功能"""
        logger.info("测试图表功能...")
        
        results = {}
        
        try:
            # 测试PyEcharts基础功能
            from pyecharts.charts import Bar, Line, Pie
            from pyecharts import options as opts
            import pandas as pd
            
            # 创建测试数据
            test_data = pd.DataFrame({
                'category': ['A', 'B', 'C', 'D'],
                'value': [10, 20, 15, 25]
            })
            
            # 测试柱状图
            try:
                bar = Bar()
                bar.add_xaxis(test_data['category'].tolist())
                bar.add_yaxis("测试数据", test_data['value'].tolist())
                bar.set_global_opts(title_opts=opts.TitleOpts(title="测试柱状图"))
                html_content = bar.render_embed()
                results["bar_chart"] = len(html_content) > 0
                logger.info("✅ 柱状图创建成功")
            except Exception as e:
                results["bar_chart"] = False
                logger.error(f"❌ 柱状图创建失败: {e}")
            
            # 测试折线图
            try:
                line = Line()
                line.add_xaxis(test_data['category'].tolist())
                line.add_yaxis("测试数据", test_data['value'].tolist())
                line.set_global_opts(title_opts=opts.TitleOpts(title="测试折线图"))
                html_content = line.render_embed()
                results["line_chart"] = len(html_content) > 0
                logger.info("✅ 折线图创建成功")
            except Exception as e:
                results["line_chart"] = False
                logger.error(f"❌ 折线图创建失败: {e}")
            
            # 测试饼图
            try:
                pie = Pie()
                pie.add("", list(zip(test_data['category'], test_data['value'])))
                pie.set_global_opts(title_opts=opts.TitleOpts(title="测试饼图"))
                html_content = pie.render_embed()
                results["pie_chart"] = len(html_content) > 0
                logger.info("✅ 饼图创建成功")
            except Exception as e:
                results["pie_chart"] = False
                logger.error(f"❌ 饼图创建失败: {e}")
                
        except ImportError as e:
            logger.error(f"❌ PyEcharts导入失败: {e}")
            results = {"bar_chart": False, "line_chart": False, "pie_chart": False}
        
        self.test_results["chart_functionality"] = results
        return results
    
    def test_offline_resources(self) -> Dict[str, bool]:
        """测试离线资源"""
        logger.info("测试离线资源...")
        
        results = {}
        
        try:
            from src.utils.offline_chart_config import check_chart_offline_status
            
            status = check_chart_offline_status()
            results["offline_ready"] = status.get("offline_ready", False)
            results["echarts_available"] = status.get("echarts_available", False)
            results["wordcloud_available"] = status.get("wordcloud_available", False)
            
            if results["offline_ready"]:
                logger.info("✅ 离线资源完全就绪")
            else:
                missing = status.get("missing_files", [])
                logger.warning(f"⚠️ 离线资源不完整，缺失: {missing}")
                
        except Exception as e:
            logger.error(f"❌ 离线资源检查失败: {e}")
            results = {"offline_ready": False, "echarts_available": False, "wordcloud_available": False}
        
        self.test_results["offline_resources"] = results
        return results
    
    def test_database_operations(self) -> Dict[str, bool]:
        """测试数据库操作"""
        logger.info("测试数据库操作...")
        
        results = {}
        
        try:
            import sqlite3
            import tempfile
            
            # 创建临时数据库
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
                db_path = tmp_db.name
            
            try:
                # 测试数据库连接
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 测试表创建
                cursor.execute('''
                    CREATE TABLE test_table (
                        id INTEGER PRIMARY KEY,
                        name TEXT,
                        value INTEGER
                    )
                ''')
                
                # 测试数据插入
                cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", ("test", 123))
                conn.commit()
                
                # 测试数据查询
                cursor.execute("SELECT * FROM test_table")
                rows = cursor.fetchall()
                
                results["connection"] = True
                results["create_table"] = True
                results["insert_data"] = True
                results["query_data"] = len(rows) > 0
                
                conn.close()
                logger.info("✅ 数据库操作测试成功")
                
            finally:
                # 清理临时文件
                if os.path.exists(db_path):
                    os.unlink(db_path)
                    
        except Exception as e:
            logger.error(f"❌ 数据库操作测试失败: {e}")
            results = {"connection": False, "create_table": False, "insert_data": False, "query_data": False}
        
        self.test_results["database_operations"] = results
        return results
    
    def test_file_operations(self) -> Dict[str, bool]:
        """测试文件操作"""
        logger.info("测试文件操作...")
        
        results = {}
        
        try:
            import pandas as pd
            import openpyxl
            import tempfile
            
            # 创建测试数据
            test_data = pd.DataFrame({
                'A': [1, 2, 3, 4],
                'B': ['a', 'b', 'c', 'd'],
                'C': [1.1, 2.2, 3.3, 4.4]
            })
            
            with tempfile.TemporaryDirectory() as tmp_dir:
                excel_path = os.path.join(tmp_dir, "test.xlsx")
                
                # 测试Excel写入
                try:
                    test_data.to_excel(excel_path, index=False)
                    results["excel_write"] = os.path.exists(excel_path)
                    logger.info("✅ Excel写入成功")
                except Exception as e:
                    results["excel_write"] = False
                    logger.error(f"❌ Excel写入失败: {e}")
                
                # 测试Excel读取
                try:
                    if results["excel_write"]:
                        read_data = pd.read_excel(excel_path)
                        results["excel_read"] = len(read_data) == len(test_data)
                        logger.info("✅ Excel读取成功")
                    else:
                        results["excel_read"] = False
                except Exception as e:
                    results["excel_read"] = False
                    logger.error(f"❌ Excel读取失败: {e}")
                    
        except Exception as e:
            logger.error(f"❌ 文件操作测试失败: {e}")
            results = {"excel_write": False, "excel_read": False}
        
        self.test_results["file_operations"] = results
        return results
    
    def calculate_overall_score(self) -> float:
        """计算总体得分"""
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            if category == "overall_score":
                continue
            
            if isinstance(tests, dict):
                for test_name, result in tests.items():
                    total_tests += 1
                    if result:
                        passed_tests += 1
        
        score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        self.test_results["overall_score"] = score
        return score
    
    def run_full_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        logger.info("开始离线部署验证...")
        
        # 运行所有测试
        self.test_core_imports()
        self.test_chart_functionality()
        self.test_offline_resources()
        self.test_database_operations()
        self.test_file_operations()
        
        # 计算总体得分
        score = self.calculate_overall_score()
        
        logger.info(f"验证完成，总体得分: {score:.1f}%")
        return self.test_results
    
    def print_validation_report(self):
        """打印验证报告"""
        print("\n" + "="*80)
        print("智能驾驶试验管控工具 - 离线部署验证报告")
        print("="*80)
        
        score = self.test_results.get("overall_score", 0)
        
        if score >= 90:
            status = "🎉 优秀"
            color = "绿色"
        elif score >= 80:
            status = "✅ 良好"
            color = "蓝色"
        elif score >= 70:
            status = "⚠️ 一般"
            color = "黄色"
        else:
            status = "❌ 需要改进"
            color = "红色"
        
        print(f"\n总体评估: {status} ({score:.1f}%)")
        
        # 详细测试结果
        categories = {
            "core_imports": "核心模块导入",
            "chart_functionality": "图表功能",
            "offline_resources": "离线资源",
            "database_operations": "数据库操作",
            "file_operations": "文件操作"
        }
        
        for category, title in categories.items():
            tests = self.test_results.get(category, {})
            if not tests:
                continue
                
            print(f"\n{title}:")
            print("-" * 40)
            
            for test_name, result in tests.items():
                status_icon = "✅" if result else "❌"
                print(f"  {status_icon} {test_name}")
        
        # 建议
        print(f"\n建议:")
        print("-" * 40)
        
        if score >= 90:
            print("🎉 软件已完全准备好离线部署！")
            print("📦 可以安全地部署到离线环境中。")
        elif score >= 80:
            print("✅ 软件基本准备好离线部署。")
            print("🔧 建议检查失败的测试项并进行修复。")
        else:
            print("⚠️ 软件还未完全准备好离线部署。")
            print("🛠️ 需要解决关键问题后再进行部署。")
        
        print("="*80)


def main():
    """主函数"""
    validator = OfflineDeploymentValidator()
    validator.run_full_validation()
    validator.print_validation_report()


if __name__ == "__main__":
    main()
